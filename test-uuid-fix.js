// Test script to verify UUID fix for room booking
const { createClient } = require('@supabase/supabase-js');

// Supabase configuration
const SUPABASE_URL = 'https://udnhkdnbvjzcxooukqrq.supabase.co';
const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InVkbmhrZG5idmp6Y3hvb3VrcXJxIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg2Njk1NTYsImV4cCI6MjA2NDI0NTU1Nn0.fUGiIMEf7xk7R0G9EFOjYkJpO3ptkrMYjnwkA-PeOPs';

// Initialize Supabase client
const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY);

// Test UUIDs from our updated AuthContext
const TEST_USER_ID = '550e8400-e29b-41d4-a716-************';
const TEST_ADMIN_ID = '550e8400-e29b-41d4-a716-************';

async function testUUIDFix() {
  console.log('🧪 Testing UUID fix for room booking...\n');

  try {
    // Test 1: Check if our UUIDs are valid format
    console.log('1. Testing UUID format validation...');
    console.log(`   User ID: ${TEST_USER_ID}`);
    console.log(`   Admin ID: ${TEST_ADMIN_ID}`);
    
    // Validate UUID format using regex
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;
    const userIdValid = uuidRegex.test(TEST_USER_ID);
    const adminIdValid = uuidRegex.test(TEST_ADMIN_ID);
    
    console.log(`   User ID valid: ${userIdValid ? '✅' : '❌'}`);
    console.log(`   Admin ID valid: ${adminIdValid ? '✅' : '❌'}`);

    // Test 2: Try to get rooms (should work)
    console.log('\n2. Testing rooms table access...');
    const { data: rooms, error: roomsError } = await supabase
      .from('rooms')
      .select('id, name, capacity')
      .limit(1);

    if (roomsError) {
      console.log('❌ Rooms query failed:', roomsError.message);
      return;
    } else {
      console.log('✅ Rooms query successful');
      if (rooms && rooms.length > 0) {
        console.log(`   Found room: ${rooms[0].name} (ID: ${rooms[0].id})`);
      }
    }

    // Test 3: Try to create a room booking with our UUID (this will likely fail due to RLS, but should show UUID format error if that's still the issue)
    console.log('\n3. Testing room booking creation with valid UUID...');
    
    if (rooms && rooms.length > 0) {
      const testBooking = {
        user_id: TEST_USER_ID,
        room_id: rooms[0].id,
        booking_date: '2025-01-20',
        start_time: '10:00',
        end_time: '11:00',
        duration_hours: 1,
        purpose: 'Test booking to verify UUID fix',
        status: 'confirmed'
      };

      const { data: booking, error: bookingError } = await supabase
        .from('room_bookings')
        .insert(testBooking)
        .select()
        .single();

      if (bookingError) {
        console.log('❌ Room booking failed:', bookingError.message);
        console.log('   Error code:', bookingError.code);
        
        // Check if it's still a UUID error
        if (bookingError.code === '22P02') {
          console.log('   ⚠️  Still getting UUID format error - need to investigate further');
        } else if (bookingError.code === '42501' || bookingError.message.includes('permission')) {
          console.log('   ✅ UUID format is now correct! (Error is due to RLS permissions, which is expected)');
        } else {
          console.log('   ℹ️  Different error - UUID format appears to be fixed');
        }
      } else {
        console.log('✅ Room booking created successfully!');
        console.log('   Booking ID:', booking.id);
      }
    }

    // Test 4: Test with old format UUID to confirm the difference
    console.log('\n4. Testing with old format UUID (should fail)...');
    
    if (rooms && rooms.length > 0) {
      const badBooking = {
        user_id: '1', // Old format that should cause UUID error
        room_id: rooms[0].id,
        booking_date: '2025-01-20',
        start_time: '11:00',
        end_time: '12:00',
        duration_hours: 1,
        purpose: 'Test booking with bad UUID',
        status: 'confirmed'
      };

      const { data: badBookingResult, error: badBookingError } = await supabase
        .from('room_bookings')
        .insert(badBooking)
        .select()
        .single();

      if (badBookingError) {
        console.log('❌ Bad UUID booking failed (expected):', badBookingError.message);
        console.log('   Error code:', badBookingError.code);
        
        if (badBookingError.code === '22P02') {
          console.log('   ✅ Confirmed: Bad UUID format still causes 22P02 error');
        }
      } else {
        console.log('⚠️  Unexpected: Bad UUID booking succeeded');
      }
    }

  } catch (error) {
    console.error('❌ Test failed with error:', error.message);
  }
}

// Run the test
testUUIDFix().then(() => {
  console.log('\n🏁 UUID fix test completed');
}).catch(error => {
  console.error('Test script error:', error);
});
