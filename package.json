{"name": "a0-project", "version": "1.0.0", "license": "0BSD", "private": true, "main": "index.ts", "scripts": {"start": "npx expo start", "android": "npx expo run:android", "ios": "npx expo run:ios", "web": "npx expo start --web"}, "dependencies": {"@expo/metro-runtime": "~4.0.1", "@expo/vector-icons": "^14.1.0", "@react-native-async-storage/async-storage": "^1.23.1", "@react-navigation/native": "^7.1.10", "@react-navigation/native-stack": "^7.3.14", "@supabase/supabase-js": "^2.49.8", "@types/react": "~18.3.12", "date-fns": "^4.1.0", "expo": "^52.0.42", "expo-font": "^13.3.1", "expo-status-bar": "~2.0.1", "react": "18.3.1", "react-dom": "^18.3.1", "react-native": "0.76.9", "react-native-gesture-handler": "^2.25.0", "react-native-reanimated": "~3.16.1", "react-native-safe-area-context": "4.12.0", "react-native-screens": "^4.11.1", "react-native-svg": "^15.12.0", "react-native-web": "~0.19.6", "sonner-native": "^0.20.0"}, "devDependencies": {"@babel/core": "^7.20.0", "@types/react": "~18.2.14", "typescript": "^5.1.3"}}